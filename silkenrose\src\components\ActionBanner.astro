<!-- ============================================ -->
<!--                   Subscribe                  -->
<!-- ============================================ -->

<section id="subscribe-2143">
    <div class="cs-container">
        <div class="cs-image-group">
            <!--Main Image-->
            <picture class="cs-picture">
                <!--Mobile Image-->
                <source media="(max-width: 600px)" srcset="https://images.unsplash.com/photo-1620916566398-39f1143ab7be?w=396&h=570&fit=crop&crop=center">
                <!--Tablet and above Image-->
                <source media="(min-width: 601px)" srcset="https://images.unsplash.com/photo-1620916566398-39f1143ab7be?w=396&h=570&fit=crop&crop=center">
                <img loading="lazy" decoding="async" src="https://images.unsplash.com/photo-1620916566398-39f1143ab7be?w=396&h=570&fit=crop&crop=center" alt="beautiful woman with makeup" width="396" height="570">
            </picture>
            <!--Cosmetics Palette-->
            <img class="cs-wheel" loading="lazy" decoding="async" src="https://images.unsplash.com/photo-1512496015851-a90fb38ba796?w=542&h=542&fit=crop&crop=center" alt="makeup palette" width="542" height="542" aria-hidden="true">
        </div>
        <div class="cs-content">
            <span class="cs-topper">Join Our Community</span>
            <h2 class="cs-title">Unlock Exclusive Beauty Secrets & Special Offers</h2>
            <p class="cs-text">
                "Subscribe to receive insider access to new product launches, expert skincare tips, and exclusive promotions reserved for our most valued clients."
            </p>
            <form class="cs-form" name="Contact Form" method="post">
                <input class="cs-input" type="email" id="cs-email-2143" name="find-us" placeholder="Enter your email address" required>
                <button class="cs-button-solid cs-submit" type="submit">Subscribe Now</button>
            </form>
        </div>
    </div>
    <!--Flower-->
    <img class="cs-flower" loading="lazy" decoding="async" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Images/Graphics/wedding-flower3.png" alt="flower" width="284" height="583" aria-hidden="true">
</section>
<style>/*-- -------------------------- -->
<---          Subscribe         -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
  #subscribe-2143 {
    padding: var(--sectionPadding);
    padding-bottom: 0;
    background-color: #FFF5F3; /* Very light peach background */
    /* clips anything overflowing */
    overflow: hidden;
    position: relative;
    z-index: 1;
  }
  #subscribe-2143 .cs-container {
    width: 100%;
    /* changes to 1280px at desktop */
    max-width: 34rem;
    margin: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 3rem;
  }
  #subscribe-2143 .cs-content {
    /* set text align to left if content needs to be left aligned */
    text-align: center;
    width: 100%;
    max-width: 39.375rem;
    display: flex;
    flex-direction: column;
    /* centers content horizontally, set to flex-start to left align */
    align-items: center;
    position: relative;
    z-index: 1;
  }
  #subscribe-2143 .cs-title {
    /* 31px - 49px */
    font-size: clamp(1.9375rem, 3.3vw, 3.0625rem);
    max-width: 30ch;
  }
  #subscribe-2143 .cs-text {
    margin-bottom: 1rem;
  }
  #subscribe-2143 .cs-text:last-of-type {
    margin-bottom: 2rem;
  }
  #subscribe-2143 .cs-form {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    /* 8px to 20px */
    gap: clamp(0.5rem, 1.5vw, 1.25rem);
  }
  #subscribe-2143 .cs-input {
    font-size: 1rem;
    width: 100%;
    /* 46px - 56px */
    height: clamp(2.875rem, 5.5vw, 3.5rem);
    margin: 0;
    /* prevents padding from adding to width and height */
    box-sizing: border-box;
    padding: 0;
    padding-left: 1.25rem;
    border: none;
    display: block;
  }
  #subscribe-2143 .cs-input::placeholder {
    color: #767676;
  }
  #subscribe-2143 .cs-button-solid {
    font-family: 'Poppins', sans-serif;
    font-size: 1rem;
    font-weight: 600;
    /* 46px - 56px */
    line-height: clamp(2.875rem, 5.5vw, 3.5rem);
    text-align: center;
    text-decoration: none;
    min-width: 9.375rem;
    margin: 0;
    /* prevents padding from adding to the width */
    box-sizing: border-box;
    padding: 0 1.5rem;
    background-color: var(--primary);
    color: #fff;
    display: inline-block;
    position: relative;
    z-index: 1;
    letter-spacing: 0.02em;
  }
  #subscribe-2143 .cs-button-solid:before {
    content: "";
    width: 0%;
    height: 100%;
    background: #E67E5B; /* Darker peach for hover */
    opacity: 1;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    transition: width 0.3s;
  }
  #subscribe-2143 .cs-button-solid:hover:before {
    width: 100%;
  }
  #subscribe-2143 .cs-submit {
    width: 100%;
    color: var(--bodyTextColorWhite);
    border: none;
  }
  #subscribe-2143 .cs-image-group {
    /* scales the whole section down and ties the font size to the vw and stops at 75% of the vale of 1em, changes at desktop */
    font-size: min(2.7vw, .7rem);
    /* everything inside this box is in ems so we can scale it all down proportionally with a font size */
    width: 33.875em;
    height: 35.625em;
    order: 2;
    position: relative;
    z-index: 1;
  }
  #subscribe-2143 .cs-picture {
    width: 26.9375em;
    height: 35.625em;
    display: block;
    position: absolute;
    bottom: 0;
    left: 0;
  }
  #subscribe-2143 .cs-picture img {
    width: auto;
    height: 100%;
    object-fit: contain;
  }
  #subscribe-2143 .cs-wheel {
    width: 33.875em;
    height: 33.875em;
    object-fit: cover;
    position: absolute;
    top: 3.4375em;
    right: 0;
    z-index: -2;
  }
  #subscribe-2143 .cs-flower {
    width: 17.75rem;
    height: auto;
    display: none;
    position: absolute;
    z-index: -1;
  }
}
/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
  #subscribe-2143 .cs-image-group {
    /* set to inherit at Large Desktop */
    font-size: min(1.32vw, 1rem);
  }
  #subscribe-2143 .cs-form {
    flex-direction: row;
  }
  #subscribe-2143 .cs-button-solid {
    width: auto;
  }
}
/* Desktop - 1024px */
@media only screen and (min-width: 64rem) {
  #subscribe-2143 .cs-container {
    max-width: 80rem;
    flex-direction: row;
    justify-content: center;
    gap: 6.25rem;
  }
  #subscribe-2143 .cs-content {
    text-align: left;
    max-width: 45%;
    align-items: flex-start;
  }
}
/* Large Desktop - 1440px */
@media only screen and (min-width: 90rem) {
  #subscribe-2143 .cs-flower {
    display: block;
    top: 1rem;
    left: 0;
  }
}
</style>