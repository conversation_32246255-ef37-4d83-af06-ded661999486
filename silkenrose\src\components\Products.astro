<!-- ============================================ -->
<!--                 Collection                   -->
<!-- ============================================ -->
---
// Import product data
import { products } from '../data/products.js';
---

<section id="collection-1556">
    <div class="cs-container">
        <div class="cs-content">
            <h2 class="cs-title">Luxury Beauty Collection</h2>
            <a href="/productoverview" class="cs-button-solid">View All Products</a>
        </div>
        <div class="cs-card-group">
            {products.map((product) => (
                <li class="cs-item">
                    <a href={`/products/${product.slug}`} class="cs-link">
                        <picture class="cs-picture">
                            <!--Mobile Image-->
                            <source media="(max-width: 600px)" srcset={product.images.mobile}>
                            <!--Tablet and above Image-->
                            <source media="(min-width: 601px)" srcset={product.images.main}>
                            <img loading="lazy" decoding="async" src={product.images.main} alt={product.name} width="600" height="600">
                        </picture>
                        <h3 class="cs-product-name">{product.name}</h3>
                        <span class="cs-price">{product.price}</span>
                    </a>
                </li>
            ))}
        </div>
    </div>
</section>

<style>/*-- -------------------------- -->
<---        Collection          -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
  #collection-1556 {
    padding: var(--sectionPadding);
    padding-top: 8rem; /* Account for fixed navigation */
  }
  #collection-1556 .cs-container {
    width: 100%;
    max-width: 80rem;
    margin: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    /* 48px - 64px */
    gap: clamp(3rem, 6vw, 4rem);
  }
  #collection-1556 .cs-content {
    /* set text align to left if content needs to be left aligned */
    text-align: center;
    width: 100%;
    /* breaks up the parent div so the children are no longer bound by it.  Now the children can be children of the cs-container and allow us to arrnage their order with the cs-card-group */
    display: contents;
    flex-direction: column;
    /* centers content horizontally, set to flex-start to left align */
    align-items: center;
  }
  #collection-1556 .cs-title {
    margin: 0;
  }
  #collection-1556 .cs-button-solid {
    font-family: 'Poppins', sans-serif;
    font-size: 1rem;
    /* 46px - 56px */
    line-height: clamp(2.875rem, 5.5vw, 3.5rem);
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    margin: 0;
    color: #fff;
    min-width: 9.375rem;
    padding: 0 1.5rem;
    background-color: var(--primary);
    display: inline-block;
    position: relative;
    z-index: 1;
    /* prevents padding from adding to the width */
    box-sizing: border-box;
    letter-spacing: 0.02em;
  }
  #collection-1556 .cs-button-solid:before {
    content: '';
    position: absolute;
    height: 100%;
    width: 0%;
    background: #E67E5B; /* Darker peach for hover */
    opacity: 1;
    top: 0;
    left: 0;
    z-index: -1;
    transition: width .3s;
  }
  #collection-1556 .cs-button-solid:hover:before {
    width: 100%;
  }
  #collection-1556 .cs-button-solid {
    order: 3;
  }
  #collection-1556 .cs-card-group {
    width: 100%;
    margin: 0;
    padding: 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(19.0625rem, 1fr));
    /* 16px - 20px */
    column-gap: clamp(1rem, 3vw, 1.25rem);
    /* 16px - 40px */
    row-gap: clamp(1rem, 3vw, 2.5rem);
  }
  #collection-1556 .cs-item {
    list-style: none;
    margin: 0;
    padding: 0;
  }
  #collection-1556 .cs-item:hover .cs-picture {
    background-color: rgba(0, 0, 0, 0.1);
  }
  #collection-1556 .cs-item:hover .cs-picture img {
    transform: scale(1.05);
  }
  #collection-1556 .cs-link {
    text-decoration: none;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
  #collection-1556 .cs-picture {
    width: 100%;
    height: 100vw;
    min-height: 21.25rem;
    max-height: 22.5rem;
    /* 12px - 20px */
    margin: 0 0 clamp(0.75rem, 2vw, 1.25rem);
    background-color: #FFF5F3; /* Very light peach background */
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 1;
    transition: background-color 0.3s;
  }
  #collection-1556 .cs-picture img {
    width: 100%;
    max-width: 15.625rem;
    height: auto;
    max-height: 80%;
    object-fit: contain;
    transition: transform .3s;
  }
  #collection-1556 .cs-product-name {
    font-family: 'Playfair Display', serif;
    /* 20px - 25px */
    font-size: clamp(1.25rem, 3vw, 1.5625rem);
    line-height: 1.2em;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
    color: var(--headerColor);
    letter-spacing: -0.01em;
  }
  #collection-1556 .cs-price {
    font-family: 'Poppins', sans-serif;
    font-size: 1rem;
    line-height: 1.2em;
    font-weight: 600;
    margin: 0;
    color: var(--primary);
    display: block;
    letter-spacing: 0.01em;
  }
}
/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
  #collection-1556 .cs-content {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    gap: 3rem;
  }
}